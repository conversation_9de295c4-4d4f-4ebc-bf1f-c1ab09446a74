package hc.ci360.agent.agent_ch_stream;

import com.sas.mkt.agent.sdk.CI360Agent;
import com.sas.mkt.agent.sdk.CI360AgentException;
import com.sas.mkt.agent.sdk.CI360StreamInterface;
import com.sas.mkt.agent.sdk.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.net.URL;
import java.net.URLClassLoader;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Enumeration;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.jar.Manifest;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import hc.ci360.agent.common.SqlConnectionPool;
import hc.ci360.agent.common.Util;
import hc.ci360.agent.common.Log;
import java.util.Enumeration;

/**
 * Class for connecting to CI360 and receiving events payload data
 */
public class Agent {
    private static final Logger logger = (Logger) LoggerFactory.getLogger(Agent.class);

    private static CI360Agent agent;
    private static CI360StreamInterface streamListener;

    static private AtomicBoolean alreadySeenStreamClosedCall = new AtomicBoolean(false);
    public static AtomicBoolean Exiting=new AtomicBoolean(false);

    public static boolean testMode = false;

    public static void main(String[] args) throws Exception {

        String gatewayHost="";
        String tenantID="";
        String clientSecret="";
        int maxThreads = 0;

        try {
            Runtime.getRuntime().addShutdownHook(new ShutdownHook());	//kill signal handling, stop application

            logger.info("");
            logger.info("----- starting -------------------------------------------------------");

            //log jar path
            String jarPath = Agent.class
                    .getProtectionDomain()
                    .getCodeSource()
                    .getLocation()
                    .toURI()
                    .getPath();
            logger.info("App: " + jarPath);

            //log version from manifest entry
            Enumeration<URL> resources = Agent.class.getClassLoader().getResources("META-INF/MANIFEST.MF");

            Manifest manifest = new Manifest(resources.nextElement().openStream());
            String version = manifest.getMainAttributes().getValue("version");
            logger.info("Version: " + version );

            logger.info("----- configuration --------------------------------------------------");
            //load config
            Log log = new Log();

            //agent properties
            gatewayHost = log.loadKeyValue("ci360.gatewayHost");
            tenantID = log.loadKeyValue("ci360.tenantID");
            clientSecret = log.loadKeyValueBase64("ci360.clientSecret");
            logger.info("ci360.clientSecret={}....{}",clientSecret.substring(0, 4),clientSecret.substring(clientSecret.length()-4, clientSecret.length()));
            String proxyServer = log.loadKeyValue("proxy.server");
            String proxyPort = log.loadKeyValue("proxy.port");
            maxThreads = log.loadKeyValueAsInteger("agent.max_threads");

            //set proxy server
            if(!proxyServer.equals("")) {
                System.setProperty("http.proxyHost", proxyServer);
                System.setProperty("http.proxyPort", proxyPort);
            }

            //!!! AGENT TEST, USE FOR TESTING, WITHOUT CONNECTING TO CI360
            String testModeProperty = System.getProperty("testMode");
            String testPayloadProperty = System.getProperty("testPayload");
            if(testModeProperty != null) {
                testMode = Boolean.parseBoolean(testModeProperty);
            }
            logger.info("test mode = {}",testMode);
            //eof test inicialization


            //init other classes
            TaskCache.init(log);
            SqlConnectionPool.init(log, testMode);
            Event.init(log);

            logger.info("----- eof configuration --------------------------------------------------");

            if(testMode) {
                Test.test(testPayloadProperty);
                return;
            }
        }
        catch(Exception e) {
            logger.error("Inicialization error. " + e.toString());
            logger.info("Exit code: 1");
            Util.logErrorWithStack(e, "Exception", logger);
            System.exit(1);
        }

        try {

            Properties props = System.getProperties();
            props.setProperty("ci360.gatewayHost", gatewayHost);
            props.setProperty("ci360.tenantID", tenantID);
            props.setProperty("ci360.clientSecret", clientSecret);

            //start Agent
            agent=new CI360Agent();
            ExecutorService executorService = Executors.newFixedThreadPool(maxThreads);
            streamListener=new CI360StreamInterface() {
                public boolean processEvent(final String event) {

                    Runnable processEvent = new Event(event);
                    executorService.submit(processEvent);
                    return true;
                }

                public void streamClosed(ErrorCode errorCode, String message) {
                    if (Exiting.get() ) {
                        logger.debug("Stream closed");
                    } else {
                        logger.error("Stream to cloud closed, "+ errorCode + ": " + message);
                        if (alreadySeenStreamClosedCall.compareAndSet(false, true)) {
                            logger.debug("Passed compareAndSet test");
                            logger.info("Waiting 15000 ms before reconnecting");
                            final CI360StreamInterface ci360StreamInterface = this;
                            Thread startThread= new Thread(() -> {
                                try {
                                    Thread.sleep(15000);
                                } catch (InterruptedException e) {

                                }
                                alreadySeenStreamClosedCall.set(false);
                                try {
                                    agent.startStream(ci360StreamInterface, true);
                                } catch (CI360AgentException e) {
                                    logger.error("ERROR " + e.getErrorCode() + ": " + e.getMessage());
                                }
                            });
                            startThread.setName("AgentRestart-");
                            startThread.start();
                        }
                    }
                }
            };
            agent.startStream(streamListener, true);



            if (System.console() != null) {
                //Console mode
                System.console().readLine();
                Exiting.set(true);
                stop("console", 0);
            }else {
                //background service mode
                LocalDateTime lastAlive= LocalDateTime.now();
                while(true) {

                    //each minute log the app is running
                    LocalDateTime now = LocalDateTime.now();
                    if(Duration.between(lastAlive, now).toMinutes()>=1) {
                        logger.info("running");
                        lastAlive= now;
                    }

                    if(Exiting.get()) {
                        executorService.shutdown();
                        stop("signal", 0);
                    }
                    Thread.sleep(1000);
                }
            }


        } catch (Exception e) {
            logger.error("Agent generic error. " + e.toString());
            Util.logErrorWithStack(e, "Exception", logger);
            stop("error", 1);
        }


    }

    public static void stop(String reason, int rc) {
        try {
            logger.info("Stop reason: '{}'", reason);

            //close Sql connection pool
            SqlConnectionPool.stop();

            //exiting=true;
            if (agent != null)
                agent.stopStream();
            Thread.sleep(1000);
            logger.info("Exit code: {}", rc);
        } catch (Exception e) {
            logger.error("Error when closing stream to cloud, " + e.getMessage());
            Util.logErrorWithStack(e, "Exception", logger);
            rc = 1;
            logger.info("Exit code: {}", rc);
        } finally {
            System.exit(rc);
        }
    }
}

