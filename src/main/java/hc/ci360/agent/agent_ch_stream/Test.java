package hc.ci360.agent.agent_ch_stream;

import hc.ci360.agent.common.Util;
import org.json.JSONObject;

public class Test {
    public static void test(String payloadFile) throws Exception {
        //String file = "./message_examples/"+fileName;
        String file = "./"+payloadFile;
        JSONObject payload = Util.loadJSONFile(file);
        String eventPayload=payload.toString();

        //test message processing
        Thread event = new Event(eventPayload);
        event.start();
    }
}
