package hc.ci360.agent.agent_ch_stream;

import java.sql.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Properties;

import hc.ci360.agent.common.Log;
import hc.ci360.agent.common.Util;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import hc.ci360.agent.common.Http;
import hc.ci360.agent.common.SqlConnectionPool;
//import kb.ci360.common.Util;

public class TaskCache {
    private static final Logger logger =  LoggerFactory.getLogger(TaskCache.class);

    private static HashMap<String,TaskEntry> cache= new HashMap<String,TaskEntry>() ;	//Task CP cache !!!!!!!!!!
    private static HashMap<String, Object> locks = new HashMap<String, Object>();		//used to synchronizace processing each taskVersionId (one thread in one moment)

    private static String ci360Server;
    private static String ci360Token;
    private static String proxyServer;
    private static int proxyPort;

    public static void init(Log log)  {

        //log configuration
        ci360Server = log.loadKeyValue("ci360.gatewayHost");
        ci360Token = log.loadKeyValueBase64("ci360.token");

        proxyServer = log.loadKeyValue("proxy.server");
        proxyPort = Integer.parseInt(log.loadKeyValue("proxy.port"));
    }

    public static TaskEntry getTask(String taskVersionId, String taskId) {

        LocalDateTime start = LocalDateTime.now();
        //add taskVersionId to locks array if missing, used to synchronizace processing of taskVersionId (one taskVersionId just in one thread in one moment)
        synchronized(locks) {
            if(locks.get(taskVersionId)==null)
            {
                locks.put(taskVersionId, new Object());
            }
        }

        synchronized(locks.get(taskVersionId)) {

            //get task from cache in memory
            TaskEntry te = cache.get(taskVersionId);

            //task is in application cache
            if(te != null) {
                logger.debug("TaskVersionId=" +taskVersionId + " loaded from application cache, Cell package SK=" + te.CELL_PACKAGE_SK + ", duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                return te;
            }


            //if not exist load from CDM batch tables
            try {
                te = TaskCache.loadDatabaseTaskCp(taskVersionId,taskId);
                if(te != null) {
                    logger.debug("TaskVersionId=" +taskVersionId + " loaded from CDM Batch table, Cell package SK=" + te.CELL_PACKAGE_SK + ", duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                    addTaskEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                Util.logErrorWithStack(e, "Exception()", logger);
            }


            //if not exist read from CI 360 API
            try {
                te = TaskCache.loadCI360ApiTaskCp(taskVersionId,taskId);
                if(te != null) {
                    logger.debug("TaskVersionId=" +taskVersionId + " loaded from CI360 API, Cell package SK=" + te.CELL_PACKAGE_SK + ", and saved to TaskCache table, duration[ms]: " + Duration.between(start, LocalDateTime.now()).toMillis()  );
                    saveTaskCpToCache(te);
                    addTaskEntryToCache(te);
                    return te;
                }
            } catch (Exception e) {
                Util.logErrorWithStack(e, "Exception()", logger);
            }

            return null;		//error
        }

    }
    private static void addTaskEntryToCache(TaskEntry te) {
        cache.put(te.TaskVersionId, te);
    }


    private static TaskEntry loadDatabaseTaskCp(String taskVersionId, String taskId) throws SQLException, InterruptedException, ClassNotFoundException {

        //save message to DB table queue
        String db = SqlConnectionPool.database;
        String sql="SELECT attribute_nm,attribute_val FROM " + db + ".cie_contact_history_stream_task_cp_cache WHERE task_version_id=?";

        Connection con = SqlConnectionPool.getConnection();
        try (
                PreparedStatement st = con.prepareStatement(sql);
        )
        {
            /*input parameter, batch size*/
            st.setString(1,taskVersionId);	//Status New

            ResultSet rs = st.executeQuery() ;

            if(rs.next()!=false) {	//test if rs is not empty
                TaskEntry te = new TaskEntry();
                te.TaskVersionId=taskVersionId;
                te.TaskId=taskId;
                te.CELL_PACKAGE_SK = null;		//just init value

                do {	//for each custom property
                    String name=rs.getString("attribute_nm");
                    String value=rs.getString("attribute_val");     //value from DB can be null, null is legal value

                    switch(name) {
                        case "CELL_PACKAGE_SK":
                            te.CELL_PACKAGE_SK = value;
                            return te;
                    }

                } while (rs.next());

            }
        }
        catch(SQLException e)
        {
            Util.logErrorWithStack(e, "Exception()", logger);
        }
        finally{
            SqlConnectionPool.releaseConnection(con);
        }

        return null;
    }

    public static TaskEntry loadCI360ApiTaskCp(String taskVersionId, String taskId) {

        Http http = new Http(proxyServer, proxyPort,0,true);
        String url="https://" + ci360Server  + "/marketingDesign/tasks/"+taskId;
        String[] resp= http.get(url, "application/json", "Bearer "+ ci360Token);

        int httpStatus = Integer.valueOf(resp[0]);
        String httpResponse=resp[1];

        try {

            logger.debug("Task API json: " + httpResponse);

            //parse from json offer_id and channel
            JSONObject task = new JSONObject(httpResponse);

            //init
            String CELL_PACKAGE_SK = null;

            JSONArray cp = task.getJSONArray("customProperties");

            //for each custom property
            for (int i = 0 ; i < cp.length(); i++) {

                JSONObject obj = cp.getJSONObject(i);
                String cp_name=obj.getString("propertyName");
                if(cp_name.equalsIgnoreCase("cell_package_list")) {
                    JSONArray ar = obj.getJSONArray("propertyValue");
                    CELL_PACKAGE_SK=ar.get(0).toString(); 			//array, read first value //TODO must be tested
                    if(CELL_PACKAGE_SK.equals("null")) CELL_PACKAGE_SK = null;

                    //task entry
                    TaskEntry te= new TaskEntry();
                    te.TaskId=taskId;
                    te.TaskVersionId=taskVersionId;
                    te.CELL_PACKAGE_SK = CELL_PACKAGE_SK; //can be null
                    return te;
                }
            }
        }
        catch(Exception e)
        {
            Util.logErrorWithStack(e, "Exception()", logger);
        }

        //default value for task entyt is null
        TaskEntry te= new TaskEntry();
        te.TaskId=taskId;
        te.TaskVersionId=taskVersionId;
        te.CELL_PACKAGE_SK = null;
        return te;
    }

    public static void saveTaskCpToCache(TaskEntry te) throws SQLException, InterruptedException, ClassNotFoundException {

        String db = SqlConnectionPool.database;
        String sql="INSERT INTO " + db + ".cie_contact_history_stream_task_cp_cache  (task_version_id,attribute_nm,attribute_val)    VALUES(?,?,?)";
        Connection con = SqlConnectionPool.getConnection();
        try (
                PreparedStatement st = con.prepareStatement(sql);
        )
        {
            st.setString(1, te.TaskVersionId);
            st.setString(2, "CELL_PACKAGE_SK");
            if(te.CELL_PACKAGE_SK == null) {
                st.setNull(3, Types.VARCHAR);
            }else {
                st.setString(3, te.CELL_PACKAGE_SK);
            }
            st.addBatch();

            st.executeBatch();
        }
        catch(SQLException e)
        {
            Util.logErrorWithStack(e, "Exception()", logger);
        }
        finally{
            SqlConnectionPool.releaseConnection(con);
        }

    }
}
