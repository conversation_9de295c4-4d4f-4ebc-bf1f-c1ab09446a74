package hc.ci360.agent.agent_ch_stream;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import hc.ci360.agent.common.Log;
import hc.ci360.agent.common.SqlConnectionPool;
import hc.ci360.agent.common.Util;


public class Event extends Thread  {
    private static final Logger logger = (Logger) LoggerFactory.getLogger(Event.class);

    private String eventString;

    private static String processEventWithPrefix;
    private static String ignoreEvents;
    private static String directEventName;

    public static void init(Log log)  {

        processEventWithPrefix = log.loadKeyValue("event.process_event_with_prefix");
        directEventName = log.loadKeyValue("event.direct_event");
        ignoreEvents = log.loadKeyValue("event.ignore_events").toLowerCase();
    }

    protected Event(String eventString ) {
        this.eventString = eventString;
    }

    public void run() {
        //configuration message from ci360, just log in debug mode
        if (eventString.startsWith("CFG")) {
            logger.debug("Event: '{}'", eventString);
            return;
        }

        try {
            //test event value
            JSONObject jsonEvent = new JSONObject(eventString);
            JSONObject attr= jsonEvent.getJSONObject("attributes");

            if(attr.has("event")){
                String event =attr.getString("event");

                //test event by prefix
                if(event.toLowerCase().startsWith(processEventWithPrefix)) {
                    //test ignored messages
                    if(ignoreEvents.contains(event.toLowerCase())) {
                        logger.debug("Event received, event: {}, event will be ignored, stop processing" , event);
                    }else {
                        logger.info("Event received, event: {}, will be processed..." , event);
                        logger.debug("Received payload: \r" + eventString);

                        processMessage(attr);
                    }

                }else {
                    logger.error("ErrorCode:CH_01 - Event name is not matching with c_% prefix. Event received, event: {}, unknown event, stop processing" , event);
                }
            }else {
                logger.error("Missing 'event' element in received payload: {} ",eventString);
            }

        } catch (Exception e) {
            logger.error("Message processing error. " + e.toString());
            Util.logErrorWithStack(e, "Exception", logger);
            logger.debug("Received payload: \r" + eventString);
        }
    }

    private void processMessage(JSONObject attr) throws Exception {

        HashMap<String, Object> params=	evaluateParameters(attr);

        boolean valid=validateParameters(params);

        if(valid){
            saveToDb(params);
        }
    }

    private HashMap<String, Object> evaluateParameters(JSONObject jsonEvent)  {

        HashMap<String, Object> pars = new HashMap<String, Object>();

        pars.put("CONTACT_ID", Util.jsonReadString(jsonEvent, "guid"));

        //cloud unix timestamp -> contact_dttm and contact_dt
        String timestamp=Util.jsonReadString(jsonEvent,"timestamp");
        Timestamp contact_dttm = new Timestamp(Long.parseLong(timestamp));
        pars.put("CONTACT_DTTM",contact_dttm);
        LocalDateTime localDateTime = contact_dttm.toLocalDateTime();
        LocalDate localDate = localDateTime.toLocalDate();
        Timestamp contact_dt = Timestamp.valueOf(localDate.atStartOfDay());
        pars.put("CONTACT_DT",contact_dt);

        pars.put("IDENTITY_ID", Util.jsonReadString(jsonEvent, "datahub_id"));
        pars.put("SUBJECT_ID", Util.jsonReadString(jsonEvent, "subject_id"));
        pars.put("CUSTOMER_ID", Util.jsonReadString(jsonEvent, "customer_id"));
        pars.put("VISITOR_ID", Util.jsonReadString(jsonEvent, "vid"));
        pars.put("LOGIN_ID", Util.jsonReadString(jsonEvent, "login_id"));
        pars.put("TASK_ID", Util.jsonReadString(jsonEvent, "task_id"));
        pars.put("CREATIVE_ID", Util.jsonReadString(jsonEvent, "creative_id"));
        pars.put("MESSAGE_ID", Util.jsonReadString(jsonEvent, "message_id"));
        pars.put("CI360_CONTACT_CHANNEL_NM", Util.jsonReadString(jsonEvent, "channelType"));

        pars.put("CONTACT_CHANNEL_CD", getContactChannel(jsonEvent));

        pars.put("RTC_ID", Util.jsonReadString(jsonEvent, "response_tracking_code"));
        pars.put("SESSION_ID", Util.jsonReadString(jsonEvent, "session"));
        pars.put("OCCURRENCE_ID", Util.jsonReadString(jsonEvent, "occurrence_id"));
        pars.put("TASK_VERSION_ID", Util.jsonReadString(jsonEvent, "task_version_id"));
        pars.put("SPOT_ID", Util.jsonReadString(jsonEvent, "spot_id"));
        pars.put("IMPRINT_ID", Util.jsonReadString(jsonEvent, "imprint_id"));
        pars.put("CREATIVE_VERSION_ID", Util.jsonReadString(jsonEvent, "creative_version_id"));
        pars.put("CONTACT_STATUS_CD", "_11");

        pars.put("RESPTRACKING_CD", Util.jsonReadString(jsonEvent, "RESPTRACKING_CD"));

        //lead_id
        String leadId = getLeadId(jsonEvent);
        pars.put("LEAD_ID", leadId);

        Timestamp tsNow = new Timestamp(System.currentTimeMillis());
        pars.put("INSERTED_DTTM", tsNow);
        pars.put("UPDATED_DTTM", tsNow);

        pars.put("OFFER_ID", getOfferId(jsonEvent));
        pars.put("CELL_PACKAGE_SK", getCellPackageSk(jsonEvent));    // e.g.  "TASKPROP_cell_package_list": "\"278200\""  -> number 278200

        return pars;
    }

    private Object getCellPackageSk(JSONObject jsonEvent) {
        //load from payload
        String eventName = Util.jsonReadString(jsonEvent, "eventName");
        String cellPackageStr = Util.jsonReadString(jsonEvent, "TASKPROP_cell_package_list").replace("\"", "");

        if(
                (cellPackageStr == null || cellPackageStr.isEmpty()) &&
                eventName.equalsIgnoreCase("c_send")
          ) {
            //load from TaskCache/DB/CI360 api
            String taskId = Util.jsonReadString(jsonEvent, "task_id");
            String taskVersionId = Util.jsonReadString(jsonEvent, "task_version_id");

            TaskEntry te = TaskCache.getTask(taskVersionId, taskId);
            if(te != null && te.CELL_PACKAGE_SK != null && !te.CELL_PACKAGE_SK.isEmpty()) {
                cellPackageStr = te.CELL_PACKAGE_SK;
            }
        }


        Long cellPackageNum;
        try {
            cellPackageNum = Long.valueOf(cellPackageStr);
        } catch (Exception e) {
            cellPackageNum = null;
        }
        return cellPackageNum;
    }

    private String getOfferId(JSONObject jsonEvent) {
        String res = Util.jsonReadString(jsonEvent, "offer_id");
        if (! res.equals(""))
            return res;

        res = Util.jsonReadString(jsonEvent, "OFFER_ID");
        if (! res.equals(""))
            return res;

        res = Util.jsonReadString(jsonEvent, "offerid");
        if (! res.equals(""))
            return res;

        res = Util.jsonReadString(jsonEvent, "OFFERID");
        if (! res.equals(""))
            return res;

        return "";
    }
    private String getContactChannel(JSONObject jsonEvent) {

        String channelCode = Util.jsonReadString(jsonEvent, "TASKPROP_channel");
        String channelType = Util.jsonReadString(jsonEvent, "channelType");
        String eventName = Util.jsonReadString(jsonEvent, "eventName");
        String task_type = Util.jsonReadString(jsonEvent, "task_type");
        String customChannelTSO = Util.jsonReadString(jsonEvent, "TASKPROP_custom_channel_TSO");

        if (eventName.equalsIgnoreCase("c_advertisingAudienceUpload") && task_type.equalsIgnoreCase("Google Ads")) {
            return "GGL";
        }
        if (eventName.equalsIgnoreCase("c_advertisingAudienceUpload") && task_type.equalsIgnoreCase("Facebook Audience")) {
            return "FCB";
        }
        if (eventName.equalsIgnoreCase("c_send") && channelType.equalsIgnoreCase("email")) {
            return "EML";
        }
        if (eventName.equalsIgnoreCase("c_inAppSend") && channelType.equalsIgnoreCase("mobile")) {
            return "IAP";
        }
        if (eventName.equalsIgnoreCase("c_impression_viewable") && channelType.equalsIgnoreCase("web")) {
            return "WEB";
        }
        if (eventName.equalsIgnoreCase("c_impression_viewable") && channelType.equalsIgnoreCase("mobile")) {
            return "MBA";
        }

        String res = null;

        switch(channelCode) {
            case "1":
                res = "SMS";
                break;
            case "2":
                res = "WTS";
                break;
            case "3":
                res = "VIB";
                break;
            case "4":
                res = "ZNS";
                break;
            case "5":
                res = "TSO";
                break;
            case "6":
                res = "EML";
                break;
            case "7":
                res = "PSH";
                break;
            case "8":
                res = "IBM";
                break;
            case "9":
                res = "GGL";
                break;
            case "10":
                res = "FCB";
                break;
        }

        if (customChannelTSO.equals("5")) {
            return "TSO";
        }

        return res;
    }

    private String getLeadId(JSONObject jsonEvent) {

        String leadId;

        //test if direct event
        boolean directEvent = false;
        String event = Util.jsonReadString(jsonEvent, "event");
        if(event.equalsIgnoreCase(directEventName)) {
            directEvent = true;
        }

        String rtc = Util.jsonReadString(jsonEvent, "response_tracking_code");
        String datahub = Util.jsonReadString(jsonEvent, "datahub_id");
        String timestamp=Util.jsonReadString(jsonEvent,"timestamp");

        if(directEvent) {
            leadId = datahub + "_" + rtc;
        }else {
            leadId = datahub + "_" + rtc + "_" + timestamp;
        }
        return leadId;
    }

    private boolean validateParameters(HashMap<String, Object> pars)  {
        String logMsg="Parameters validation: ";

        /* Mandatory field for saving message to db*/
        String mandatoryFields="CONTACT_ID|CONTACT_DT|CONTACT_DTTM|IDENTITY_ID|CI360_CONTACT_CHANNEL_NM|LEAD_ID|INSERTED_DTTM|UPDATED_DTTM";

        //check if all parameters exists
        boolean res=true;

        for (Map.Entry<String, Object> me : pars.entrySet()) {
            String key=me.getKey().toString();
            Object valObject=me.getValue();



            if(valObject == null || valObject.toString().isEmpty()) {
                //parameter without value
                if(mandatoryFields.contains(key)) {
                    //mandatory
                    res=false;
                    logMsg+=String.format("%s = %s  missing value for mandatory field !!!!!!!, ",key,"");
                }else {
                    //optional parameter
                    logMsg+=String.format("%s = %s, ",key,"");
                }
            }else {
                //parameter has value
                String valString;
                if(valObject instanceof Timestamp) {
                    Timestamp ts = (Timestamp)valObject;
                    valString = ts.toString();
                }else {
                    valString = valObject.toString();
                }

                logMsg+=String.format("%s = %s, ",key,valString);
            }


        }

        if(res) {
            logger.debug(logMsg);
            logger.debug("Validation: OK");
        }
        else {
            logger.error(logMsg);
            logger.error("ErrorCode:CH_02 - Validation error - missing mandatory attributes in payload");
        }
        return res;
    }

    private void saveToDb(HashMap<String, Object> pars ) throws Exception {

        if(Agent.testMode)
            return;

        String db = SqlConnectionPool.database;

        //save message to DB table queue
        String sql=" INSERT INTO " + db + ".CIE_CONTACT_HISTORY_STREAM (CONTACT_ID,CONTACT_DT,CONTACT_DTTM,IDENTITY_ID,SUBJECT_ID,CUSTOMER_ID,VISITOR_ID,LOGIN_ID," +
                "TASK_ID,CREATIVE_ID,MESSAGE_ID,CI360_CONTACT_CHANNEL_NM,CONTACT_CHANNEL_CD,RTC_ID,SESSION_ID," +
                "OCCURRENCE_ID,TASK_VERSION_ID,SPOT_ID,IMPRINT_ID,CREATIVE_VERSION_ID,CONTACT_STATUS_CD,LEAD_ID," +
                "INSERTED_DTTM,UPDATED_DTTM,OFFER_ID,CELL_PACKAGE_SK, RESPTRACKING_CD) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";

        Connection con = SqlConnectionPool.getConnection();
        try (
                PreparedStatement st = con.prepareStatement(sql);
        )
        {
            setStringParameter(st,1,pars.get("CONTACT_ID"));
            setTimestampParameter(st,2,pars.get("CONTACT_DT"));
            setTimestampParameter(st,3,pars.get("CONTACT_DTTM"));
            setStringParameter(st,4,pars.get("IDENTITY_ID"));
            setStringParameter(st,5,pars.get("SUBJECT_ID"));
            setStringParameter(st,6,pars.get("CUSTOMER_ID"));
            setStringParameter(st,7,pars.get("VISITOR_ID"));
            setStringParameter(st,8,pars.get("LOGIN_ID"));
            setStringParameter(st,9,pars.get("TASK_ID"));
            setStringParameter(st,10,pars.get("CREATIVE_ID"));
            setStringParameter(st,11,pars.get("MESSAGE_ID"));
            setStringParameter(st,12,pars.get("CI360_CONTACT_CHANNEL_NM"));
            setStringParameter(st,13,pars.get("CONTACT_CHANNEL_CD"));
            setStringParameter(st,14,pars.get("RTC_ID"));
            setStringParameter(st,15,pars.get("SESSION_ID"));
            setStringParameter(st,16,pars.get("OCCURRENCE_ID"));
            setStringParameter(st,17,pars.get("TASK_VERSION_ID"));
            setStringParameter(st,18,pars.get("SPOT_ID"));
            setStringParameter(st,19,pars.get("IMPRINT_ID"));
            setStringParameter(st,20,pars.get("CREATIVE_VERSION_ID"));
            setStringParameter(st,21,pars.get("CONTACT_STATUS_CD"));
            setStringParameter(st,22,pars.get("LEAD_ID"));
            setTimestampParameter(st,23,pars.get("INSERTED_DTTM"));
            setTimestampParameter(st,24,pars.get("UPDATED_DTTM"));
            setStringParameter(st, 25, pars.get("OFFER_ID"));
            setLongParameter(st,26,pars.get("CELL_PACKAGE_SK"));
            setStringParameter(st, 27, pars.get("RESPTRACKING_CD"));

            st.execute() ;
        }

        catch(SQLException e)
        {
            Util.logErrorWithStack(e, "ErrorCode:CH_03_2 - DB - not possible to write payload to DB", logger);
            throw e;
        }
        finally{
            SqlConnectionPool.releaseConnection(con);
        }
        logger.debug("Event inserted into queue");
    }

    private void setStringParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if(value == null || value.toString().isEmpty()) {
            st.setNull(index,Types.VARCHAR);
        }else {
            st.setString(index, value.toString());
        }
    }

    private void setTimestampParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if(value == null ) {
            st.setNull(index,Types.TIMESTAMP);
        }else {
            st.setTimestamp(index, (Timestamp)value);
        }
    }

    private void setLongParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if(value == null ) {
            st.setNull(index,Types.NUMERIC);
        }else {
            st.setLong(index, (Long)value);
        }
    }
}
