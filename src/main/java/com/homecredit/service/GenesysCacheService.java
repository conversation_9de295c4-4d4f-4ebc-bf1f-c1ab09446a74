package com.homecredit.service;

import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.model.calllisttype.CallListName;
import com.homecredit.model.calllisttype.CallListNameCache;
import com.homecredit.model.calllisttype.CallListType;
import com.homecredit.model.calllisttype.CallListTypeCache;
import com.homecredit.model.calllisttype.CallListTypeCategory;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnExpression("${hci.agents.GEN.enabled:false} == true or ${hci.agents.DM_GEN.enabled:false} == true")
public class GenesysCacheService {

    private final ApplicationConfig applicationConfig;
    private final DataSource dataSource;

    @PostConstruct
    public void onStartup() {
        loadCallListTypeCache();
        loadCallListNameCache();
    }

    @Scheduled(cron = "${hci.cron.load-cache}")
    public void onSchedule() {
        loadCallListTypeCache();
        loadCallListNameCache();
    }

    public void loadCallListTypeCache() {
        log.debug("Updating data from database to CallListType cache...");
        try (Connection conn = dataSource.getConnection()) {
            try (Statement stmt = conn.createStatement()) {
                try (ResultSet resultSet = stmt.executeQuery(applicationConfig.getQuery().get(Agent.GEN).getLoadCache())) {
                    CallListTypeCache.clear();
                    while (resultSet.next()) {
                        CallListType callListType = new CallListType();
                        callListType.setCallListType(resultSet.getString("call_list_type"));
                        callListType.setAttributeName(resultSet.getString("Attribute_name"));
                        callListType.setAttributeType(resultSet.getString("Attribute_type"));
                        String mandatoryFlag = resultSet.getString("Mandatory_flag");
                        if (mandatoryFlag.equalsIgnoreCase("Y")) {
                            callListType.setMandatoryFlag(true);
                        } else if (mandatoryFlag.equalsIgnoreCase("N")) {

                            callListType.setMandatoryFlag(false);
                        }
                        callListType.setCategory(CallListTypeCategory.getEnum(resultSet.getString("Category")));
                        callListType.setPayloadSection(resultSet.getString("Payload_section"));
                        CallListTypeCache.add(callListType);
                        log.debug("Updated CallListType cache: key: [{}], value [{}]", callListType.getCallListType(), callListType);
                    }
                }
            } catch (SQLException e) {
                log.error("Exception during executing query", e);
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
    }

    public void loadCallListNameCache() {
        log.debug("Updating data from database to CallListName cache...");
        try (Connection conn = dataSource.getConnection()) {
            try (Statement stmt = conn.createStatement()) {
                try (ResultSet resultSet = stmt.executeQuery(applicationConfig.getQuery().get(Agent.GEN).getLoadCacheCodeCallListName())) {
                    CallListNameCache.clear();
                    while (resultSet.next()) {
                        CallListName callListName = new CallListName();
                        callListName.setCodeCallListType(resultSet.getString("CODE_CALL_LIST_TYPE"));
                        callListName.setNameCallList(resultSet.getString("NAME_CALL_LIST"));
                        callListName.setPriority(resultSet.getLong("PRIORITY"));
                        callListName.setDailyFrom(resultSet.getLong("DAILY_FROM"));
                        callListName.setDailyTill(resultSet.getLong("DAILY_TILL"));
                        callListName.setScriptType(resultSet.getString("SCRIPT_TYPE"));
                        callListName.setCallSource(resultSet.getString("CALL_SOURCE"));
                        callListName.setCallType(resultSet.getLong("CALL_TYPE"));
                        callListName.setTplId(resultSet.getLong("TPL_ID"));
                        CallListNameCache.add(callListName);
                        log.debug("Updated CallListName cache: key: [{}], value [{}]", callListName.getTplId(), callListName);
                    }
                }
            } catch (SQLException e) {
                log.error("Exception during executing query", e);
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
    }
}
