#!/bin/bash
echo

app_name="HCI CI360 AGENT CH STREAM"
running=0

#test if start.pid exists and PID  is running
if test -f "start.pid"; 
then
	if ps -p `cat start.pid` > /dev/null 
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -Dlogback.configurationFile=/sas/ma/env/agent/ch-stream/logback.xml -DconfigFile=/sas/ma/env/agent/ch-stream/app.config  -jar /sas/ma/env/agent/ch-stream/agent-ch-stream.jar 2>&1 &  
	echo $! > /sas/ma/env/agent/ch-stream/start.pid
fi

echo
